<script lang="ts" setup>
import { ref, reactive, watch, onMounted, useTemplateRef } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import type { VxeGridInstance, VxeGridProps } from "vxe-table";
import type { ReportInfo, ReportFilterParams } from "./types";
import { Search, Refresh } from "@element-plus/icons-vue";
import {
  departmentOptions,
  statusOptions,
  getStatusTagType,
  generateMockReportData,
} from "./data/mockData";
import ChatSidebar from "./components/ChatSidebar.vue";
import ReportDetailDialog from "./components/ReportDetailDialog.vue";

defineOptions({
  // 命名当前组件
  name: "ReportManagement",
});

const router = useRouter();

// 表格实例引用
const xGridDom = useTemplateRef<VxeGridInstance>("xGridDom");

// 弹窗相关状态
const dialogVisible = ref(false);
const selectedReport = ref<ReportInfo | null>(null);

// 搜索表单数据
const searchForm = reactive<ReportFilterParams>({
  keyword: "",
  department: "",
  status: "",
});

// 模拟数据
const mockData = generateMockReportData(100);

// 表格配置
const xGridOpt: VxeGridProps = reactive({
  loading: false,
  autoResize: true,
  /** 分页配置项 */
  pagerConfig: {
    align: "right",
  },
  /** 工具栏配置 */
  toolbarConfig: {
    refresh: true,
    custom: true,
    slots: {
      buttons: "toolbar-btns",
    },
  },
  /** 自定义列配置项 */
  customConfig: {
    /** 是否允许列选中  */
    checkMethod: ({ column }: { column: any }) =>
      !["patientName"].includes(column.field),
  },
  /** 列配置 */
  columns: [
    {
      type: "checkbox",
      width: "60px",
      minWidth: "60px"
    },
    {
      field: "reportNumber",
      title: "报告号",
      width: "100px",
      minWidth: "80px",
      fixed: "left" as const
    },
    {
      field: "patientName",
      title: "姓名",
      width: "100px",
      minWidth: "80px"
    },
    {
      field: "gender",
      title: "性别",
      width: "80px",
      minWidth: "60px",
      slots: {
        default: "gender-column"
      }
    },
    {
      field: "age",
      title: "年龄",
      width: "80px",
      minWidth: "60px"
    },
    {
      field: "reportTitle",
      title: "报告标题",
      minWidth: "120px",
      slots: {
        default: "title-column"
      }
    },
    {
      field: "examTime",
      title: "检查时间",
      width: "120px",
      minWidth: "100px"
    },
    {
      field: "department",
      title: "科室",
      width: "100px",
      minWidth: "80px"
    },
    {
      field: "status",
      title: "状态",
      width: "100px",
      minWidth: "80px",
      slots: {
        default: "status-column"
      }
    },
    {
      title: "详细报告",
      width: "100px",
      minWidth: "80px",
      fixed: "right" as const,
      showOverflow: false,
      slots: {
        default: "action-column"
      }
    }
  ],
  /** 数据代理配置项（基于 Promise API） */
  proxyConfig: {
    /** 启用动态序号代理 */
    seq: true,
    /** 是否代理表单 */
    form: false,
    /** 是否自动加载，默认为 true */
    autoLoad: true,
    props: {
      total: "total"
    },
    ajax: {
      query: ({ page }: { page: any }) => {
        xGridOpt.loading = true;
        return new Promise((resolve) => {
          setTimeout(() => {
            // 模拟搜索和筛选
            let filteredData = mockData;
            
            if (searchForm.keyword) {
              filteredData = filteredData.filter(item => 
                item.patientName.includes(searchForm.keyword!) ||
                item.reportNumber.includes(searchForm.keyword!) ||
                item.reportTitle.includes(searchForm.keyword!)
              );
            }
            
            if (searchForm.department) {
              filteredData = filteredData.filter(item => 
                item.department === searchForm.department
              );
            }
            
            if (searchForm.status) {
              filteredData = filteredData.filter(item => 
                item.status === searchForm.status
              );
            }

            // 分页处理
            const total = filteredData.length;
            const startIndex = (page.currentPage - 1) * page.pageSize;
            const endIndex = startIndex + page.pageSize;
            const result = filteredData.slice(startIndex, endIndex);

            xGridOpt.loading = false;
            resolve({ total, result });
          }, 300);
        });
      }
    }
  }
});

// 搜索功能
const handleSearch = () => {
  xGridDom.value?.commitProxy("query");
};

// 重置搜索
const handleReset = () => {
  searchForm.keyword = "";
  searchForm.department = "";
  searchForm.status = "";
  handleSearch();
};

// 查看报告详情
const handleViewReport = (row: ReportInfo) => {
  selectedReport.value = row;
  dialogVisible.value = true;
};

// 保存报告
const handleSaveReport = (reportData: ReportInfo) => {
  // 在实际项目中，这里应该调用API保存数据
  // 现在只是更新本地数据
  const index = mockData.findIndex(item => item.id === reportData.id);
  if (index !== -1) {
    Object.assign(mockData[index], reportData);
  }

  // 刷新表格
  xGridDom.value?.commitProxy("query");
  ElMessage.success('报告保存成功');
};

// AI分析报告
const handleAnalyzeReport = (reportData: ReportInfo) => {
  ElMessage.info(`正在对报告 ${reportData.reportNumber} 进行AI分析...`);
  // 这里可以调用AI分析接口
};

// 监听搜索条件变化，自动搜索
watch([() => searchForm.department, () => searchForm.status], () => {
  handleSearch();
});

onMounted(() => {
  // 组件挂载后的初始化操作
});
</script>

<template>
  <div class="report-management-page">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <div class="search-form">
          <div class="search-row">
            <div class="search-item">
              <el-input
                v-model="searchForm.keyword"
                placeholder="输入患者姓名或报告号"
                clearable
                @keyup.enter="handleSearch"
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            
            <div class="search-item">
              <el-select
                v-model="searchForm.department"
                placeholder="全部科室"
                clearable
                class="search-select"
              >
                <el-option
                  v-for="option in departmentOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>
            
            <div class="search-item">
              <el-select
                v-model="searchForm.status"
                placeholder="全部状态"
                clearable
                class="search-select"
              >
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>
            
            <div class="search-actions">
              <el-button type="primary" @click="handleSearch" :icon="Search">
                查询
              </el-button>
              <el-button @click="handleReset" :icon="Refresh">
                重置
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 表格区域 -->
      <div class="table-section">
        <el-card class="table-card">
          <vxe-grid ref="xGridDom" v-bind="xGridOpt">
            <!-- 工具栏按钮 -->
            <template #toolbar-btns>
              <vxe-button status="primary" icon="vxe-icon-add">
                新增报告
              </vxe-button>
              <vxe-button status="danger" icon="vxe-icon-delete">
                批量删除
              </vxe-button>
            </template>

            <!-- 性别列 -->
            <template #gender-column="{ row, column }">
              <el-tag
                :type="row[column.field] === '男' ? 'primary' : 'danger'"
                effect="light"
                size="small"
              >
                {{ row[column.field] }}
              </el-tag>
            </template>

            <!-- 报告标题列 -->
            <template #title-column="{ row, column }">
              <el-tooltip
                :content="row[column.field]"
                placement="top"
                :disabled="row[column.field].length <= 10"
              >
                <span class="title-text">
                  {{ row[column.field] }}
                </span>
              </el-tooltip>
            </template>

            <!-- 状态列 -->
            <template #status-column="{ row, column }">
              <el-tag
                :type="getStatusTagType(row[column.field])"
                effect="light"
                size="small"
              >
                {{ row[column.field] }}
              </el-tag>
            </template>

            <!-- 操作列 -->
            <template #action-column="{ row }">
              <el-button
                link
                type="primary"
                size="small"
                @click="handleViewReport(row)"
              >
                查看详情
              </el-button>
            </template>
          </vxe-grid>
        </el-card>
      </div>

      <!-- 右侧AI助手 -->
      <ChatSidebar />
    </div>

    <!-- 报告详情弹窗 -->
    <ReportDetailDialog
      v-model:visible="dialogVisible"
      :report-data="selectedReport"
      @save="handleSaveReport"
      @analyze="handleAnalyzeReport"
    />
  </div>
</template>

<style lang="scss" scoped>
.report-management-page {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .search-section {
    .search-card {
      .search-form {
        .search-row {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;

          .search-item {
            flex: 0 0 auto;

            .search-input {
              width: 250px;
            }

            .search-select {
              width: 150px;
            }
          }

          .search-actions {
            display: flex;
            gap: 8px;
            margin-left: auto;
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;

    .table-section {
      flex: 1;
      min-width: 0;

      .table-card {
        height: 100%;

        :deep(.el-card__body) {
          height: 100%;
          padding: 0;
        }

        .title-text {
          display: inline-block;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 1200px) {
  .report-management-page {
    .main-content {
      flex-direction: column;

      .table-section {
        .table-card {
          height: 600px;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .report-management-page {
    padding: 10px;

    .search-section {
      .search-card {
        .search-form {
          .search-row {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;

            .search-item {
              .search-input,
              .search-select {
                width: 100%;
              }
            }

            .search-actions {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }

    .main-content {
      gap: 15px;

      .table-section {
        .table-card {
          height: 500px;
        }
      }
    }
  }
}
</style>
