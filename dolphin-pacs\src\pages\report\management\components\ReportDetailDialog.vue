<script lang="ts" setup>
import { ref, reactive, watch } from "vue"
import { ElMessage } from "element-plus"
import type { ReportInfo } from "../types"

defineOptions({
  name: "ReportDetailDialog"
})

interface Props {
  visible: boolean
  reportData: ReportInfo | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'save': [data: ReportInfo]
  'analyze': [data: ReportInfo]
}>()

// 弹窗显示状态
const dialogVisible = ref(false)

// 表单数据
const formData = reactive<Partial<ReportInfo>>({
  id: '',
  reportNumber: '',
  patientName: '',
  gender: '男',
  age: 0,
  reportTitle: '',
  examTime: '',
  department: '',
  status: '待写报告',
  content: '',
  diagnosis: '',
  suggestion: '',
  createTime: '',
  updateTime: ''
})

// 是否处于编辑模式
const isEditing = ref(false)

// 医生信息（模拟数据）
const doctorInfo = reactive({
  examDoctor: '张医生',
  reportDoctor: '李医生',
  reviewDoctor: '王医生'
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.reportData) {
    // 复制报告数据到表单
    Object.assign(formData, props.reportData)
    isEditing.value = false
  }
})

// 监听内部弹窗状态变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 切换编辑模式
const toggleEdit = () => {
  isEditing.value = !isEditing.value
  if (!isEditing.value) {
    // 退出编辑模式时恢复原始数据
    if (props.reportData) {
      Object.assign(formData, props.reportData)
    }
  }
}

// 保存报告
const handleSave = () => {
  if (!formData.content?.trim()) {
    ElMessage.warning('请填写超声所见内容')
    return
  }
  if (!formData.diagnosis?.trim()) {
    ElMessage.warning('请填写超声提示内容')
    return
  }

  // 更新时间
  formData.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
  
  // 发送保存事件
  emit('save', formData as ReportInfo)
  isEditing.value = false
  ElMessage.success('报告保存成功')
}

// AI分析报告
const handleAnalyze = () => {
  if (!props.reportData) return
  emit('analyze', props.reportData)
  ElMessage.info('正在进行AI分析...')
}

// 格式化时间显示
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="超声检查报告单"
    width="900px"
    :before-close="handleClose"
    class="report-detail-dialog"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
  >
    <div class="report-content" v-if="formData.id">
      <!-- 医院标题 -->
      <div class="hospital-header">
        <h2>海豚医院超声检查报告单</h2>
      </div>

      <!-- 患者基本信息 -->
      <div class="patient-info-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-group">
              <div class="info-item">
                <span class="label">姓名：</span>
                <span class="value">{{ formData.patientName }}</span>
              </div>
              <div class="info-item">
                <span class="label">性别：</span>
                <span class="value">{{ formData.gender }}</span>
              </div>
              <div class="info-item">
                <span class="label">年龄：</span>
                <span class="value">{{ formData.age }}岁</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-group">
              <div class="info-item">
                <span class="label">科室：</span>
                <span class="value">{{ formData.department }}</span>
              </div>
              <div class="info-item">
                <span class="label">检查时间：</span>
                <span class="value">{{ formData.examTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">报告时间：</span>
                <span class="value">{{ formatDateTime(formData.updateTime || formData.createTime || '') }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 报告内容区域 -->
      <div class="report-content-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="content-group">
              <h4 class="content-title">超声所见：</h4>
              <el-input
                v-model="formData.content"
                type="textarea"
                :rows="8"
                :readonly="!isEditing"
                placeholder="请输入超声检查所见内容..."
                class="content-textarea"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="content-group">
              <h4 class="content-title">超声提示：</h4>
              <el-input
                v-model="formData.diagnosis"
                type="textarea"
                :rows="8"
                :readonly="!isEditing"
                placeholder="请输入超声诊断提示..."
                class="content-textarea"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 医生信息区域 -->
      <div class="doctor-info-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="doctor-item">
              <span class="doctor-label">检查医生：</span>
              <span class="doctor-name">{{ doctorInfo.examDoctor }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="doctor-item">
              <span class="doctor-label">报告医生：</span>
              <span class="doctor-name">{{ doctorInfo.reportDoctor }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="doctor-item">
              <span class="doctor-label">审核医生：</span>
              <span class="doctor-name">{{ doctorInfo.reviewDoctor }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 弹窗底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleAnalyze" type="info" :icon="'ChatDotRound'">
          AI分析报告
        </el-button>
        <el-button 
          v-if="!isEditing" 
          @click="toggleEdit" 
          type="warning"
          :icon="'Edit'"
        >
          修改报告
        </el-button>
        <template v-else>
          <el-button @click="toggleEdit" :icon="'Close'">
            取消
          </el-button>
          <el-button @click="handleSave" type="primary" :icon="'Check'">
            保存报告
          </el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.report-detail-dialog) {
  .el-dialog {
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
    border-bottom: none;
    padding: 20px 24px;
    margin: 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: white;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 20px 24px;
    text-align: center;
  }
}

.report-content {
  padding: 24px;

  .hospital-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--el-color-primary);

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 700;
      color: var(--el-color-primary);
      letter-spacing: 2px;
    }
  }

  .patient-info-section {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--el-fill-color-extra-light);
    border-radius: 8px;
    border-left: 4px solid var(--el-color-primary);

    .info-group {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 600;
          color: var(--el-text-color-primary);
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: var(--el-text-color-regular);
          flex: 1;
        }
      }
    }
  }

  .report-content-section {
    margin-bottom: 30px;

    .content-group {
      .content-title {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        padding-left: 12px;
        border-left: 3px solid var(--el-color-primary);
      }

      .content-textarea {
        :deep(.el-textarea__inner) {
          border-radius: 8px;
          border: 2px solid var(--el-border-color-light);
          font-size: 14px;
          line-height: 1.6;
          resize: none;
          transition: all 0.3s ease;

          &:focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }

          &[readonly] {
            background-color: var(--el-fill-color-extra-light);
            cursor: default;
          }
        }
      }
    }
  }

  .doctor-info-section {
    padding: 20px;
    background: var(--el-fill-color-light);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);

    .doctor-item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;

      .doctor-label {
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-right: 8px;
      }

      .doctor-name {
        color: var(--el-text-color-regular);
        padding: 4px 12px;
        background: var(--el-bg-color);
        border-radius: 4px;
        border: 1px solid var(--el-border-color-light);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;

  .el-button {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    &.el-button--primary {
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

      &:hover {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
      }
    }

    &.el-button--warning {
      box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);

      &:hover {
        box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
      }
    }

    &.el-button--info {
      box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);

      &:hover {
        box-shadow: 0 4px 12px rgba(144, 147, 153, 0.4);
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  :deep(.report-detail-dialog) {
    .el-dialog {
      width: 95vw !important;
      margin: 5vh auto;
    }
  }

  .report-content {
    padding: 16px;

    .hospital-header {
      margin-bottom: 20px;

      h2 {
        font-size: 20px;
      }
    }

    .patient-info-section,
    .report-content-section {
      margin-bottom: 20px;

      .el-row {
        .el-col {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .report-content-section {
      .content-group {
        .content-textarea {
          :deep(.el-textarea__inner) {
            font-size: 13px;
          }
        }
      }
    }
  }

  .dialog-footer {
    flex-direction: column;
    align-items: center;

    .el-button {
      width: 100%;
      max-width: 200px;
    }
  }
}
</style>
